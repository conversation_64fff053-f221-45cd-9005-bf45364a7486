# Jorn_SublimeTabOrganizer

A Sublime Text plugin for organizing and managing tabs with an interactive markdown-based workflow.

## Features

- **Tab Overview**: View detailed information about all open tabs in a table format
- **Tab Categorization**: Tabs are automatically categorized based on their state and content
- **Interactive Tab Organization**: Export tabs to a markdown table, edit the organization, and apply changes by saving
- **Automatic Layout Management**: Automatically creates appropriate layouts when needed while preserving existing layouts when possible
- **Syntax Detection**: Automatically detects file types for both saved and unsaved files
- **Path Depth Analysis**: Automatically calculates and displays the depth of file paths
- **Session-Based Tab Metrics**: Tracks tab activity during the session (save frequency, time since last save)

## Usage

### View Tab Information

- **JornTabCategor: Show Tab Overview**: Display all tabs in a table format
- **JornTabCategor: Show Tab Overview (Sorted by Group)**: Display tabs sorted by their group
- **JornTabCategor: Show Tab Overview (Sorted by Extension)**: Display tabs sorted by file extension
- **JornTabCategor: Show Tab Overview (JSON Format)**: Display tab information in JSON format

### Organize Tabs

1. **Export tabs to markdown**:
   - Press **Ctrl+Shift+T** or run "JornTabCategor: Export Tabs to Markdown" from the Command Palette
   - For a simplified table with fewer columns, use "JornTabCategor: Export Tabs to Markdown (Simplified)"
   - This creates and opens a `tab_organization.md` file with your current tab organization
   - The order of columns in the table matches the order specified in the command

2. **Edit the organization**:
   - Modify the Group values in the markdown table
   - Rearrange rows to change the order of tabs (row order is used as the index if the "Index" column is removed)
   - Use the "Last Saved" and "Save Count" columns to identify active tabs
   - Customize column order by modifying the `columns` parameter in the command
   - Remove unnecessary columns to simplify the table (only "Group" column is required)

3. **Apply changes**:
   - Simply save the `tab_organization.md` file
   - The plugin automatically reorganizes your tabs based on the saved file
   - The markdown file remains focused so you can make additional changes
   - Repeat steps 2-3 as needed to refine your tab organization

### Keyboard Shortcut

For quick access to the tab organization workflow, use the following keyboard shortcut:

- **Ctrl+Shift+T**: Export tabs to markdown (equivalent to "Jorn: Export Tabs to Markdown")

### Simplified Table Editing

The plugin supports a streamlined workflow for organizing tabs:

- **Remove the Index Column**: When the "Index" column is removed from the table, the plugin will use the row order as the index
- **Minimal Required Columns**: Only the "Group" column is required; all other columns can be removed
- **Custom Column Order**: Arrange columns in any order that makes sense for your workflow

This flexibility allows you to create simpler, more focused tables for tab organization:

```markdown
| Group | File                  | Last Saved  | Save Count |
|-------|----------------------|-------------|------------|
| 0     | active_document.py   | 0 : <5 min  | 12         |
| 0     | related_module.py    | A : <15 min | 5          |
| 1     | reference_doc.md     | C : +hour   | 1          |
| 1     | config.json          | D : unsaved | 0          |
```

When you save this table, tabs will be organized with:
- `active_document.py` as the first tab (index 0) in group 0
- `related_module.py` as the second tab (index 1) in group 0
- `reference_doc.md` as the first tab (index 0) in group 1
- `config.json` as the second tab (index 1) in group 1

### Session-Based Tab Metrics

The plugin now tracks session-based metrics for each tab:

- **Last Saved**: Time since the tab was last saved, categorized as:
  - 0: Less than 5 minutes
  - A: Less than 15 minutes
  - B: Less than 1 hour
  - C: More than 1 hour
  - D: Unsaved

- **Save Count**: Number of times the tab has been saved in the current session

These metrics enable new organization capabilities:

- Sort tabs by recent activity or save frequency
- Identify your most active files at a glance
- Make organization decisions based on tab usage patterns

The session metrics are reset when Sublime Text is restarted.

### Debug Mode

For troubleshooting or development purposes, debug mode commands are available:

- **JornTabCategor: Debug Mode - Export Tabs to Markdown**: Export tabs with detailed debug logging
- **JornTabCategor: Debug Mode - Import Tabs from Markdown**: Import tabs with detailed debug logging

When debug mode is enabled, the plugin will output detailed information about each step of the process to the Sublime Text console (View > Show Console).

## Project Structure

The plugin is designed with a modular structure:

- `JornTabCategorizer.py`: Main plugin file with commands and event listeners
- `lib/get/`: Contains utility functions for retrieving information
  - `categories.py`: Functions to categorize tabs

## Development

This plugin is designed to be extensible. The getter functions in the `lib/get/` directory can be imported and used by other plugins or additional functionality.

## License

MIT
