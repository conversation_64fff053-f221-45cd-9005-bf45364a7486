#!/usr/bin/env python3
"""
Simple Window Organizer - Export/Edit/Import approach

This module provides a simple way to organize windows by:
1. Exporting window list to a text file
2. User manually reorders the list
3. Importing the reordered list for tiling

Inspired by the elegant simplicity of Sublime Tab Organizer.
"""

import os
import json
from typing import List, Dict, Any


class WindowExporter:
    """Handles exporting window information to a file for manual editing."""
    
    def __init__(self, output_file="window_order.txt"):
        self.output_file = output_file
    
    def export_windows(self, windows: List, group_name: str = "Windows") -> str:
        """
        Export windows to a simple text file format.
        
        Args:
            windows: List of Window objects
            group_name: Name of the window group
            
        Returns:
            Path to the exported file
        """
        content = self._generate_content(windows, group_name)
        
        with open(self.output_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return os.path.abspath(self.output_file)
    
    def _generate_content(self, windows: List, group_name: str) -> str:
        """Generate the file content with window information."""
        lines = []
        
        # Header
        lines.append(f"# Window Organization - {group_name}")
        lines.append(f"# Total windows: {len(windows)}")
        lines.append("#")
        lines.append("# Instructions:")
        lines.append("# - Reorder lines to change tiling order")
        lines.append("# - First window goes to top-left, second to top-right, etc.")
        lines.append("# - Lines starting with # are comments (ignored)")
        lines.append("# - Save this file and return to Window Tiler")
        lines.append("#")
        lines.append("")
        
        # Window list
        for i, window in enumerate(windows, 1):
            # Create a simple, readable format
            title = window.title[:60] + "..." if len(window.title) > 60 else window.title
            process = window.process_name
            window_type = getattr(window, 'type', 'Unknown')
            
            # Format: [Index] Process | Type | Title
            line = f"{i:2d}. {process:20s} | {window_type:10s} | {title}"
            lines.append(line)
        
        lines.append("")
        lines.append("# End of window list")
        
        return "\n".join(lines)


class WindowImporter:
    """Handles importing and parsing the edited window file."""
    
    def __init__(self, input_file="window_order.txt"):
        self.input_file = input_file
    
    def import_window_order(self, original_windows: List) -> List:
        """
        Import the edited window order and return reordered windows.
        
        Args:
            original_windows: Original list of Window objects
            
        Returns:
            Reordered list of Window objects
        """
        if not os.path.exists(self.input_file):
            return original_windows
        
        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return self._parse_content(content, original_windows)
        
        except Exception as e:
            print(f"Error importing window order: {e}")
            return original_windows
    
    def _parse_content(self, content: str, original_windows: List) -> List:
        """Parse the file content and return reordered windows."""
        lines = content.strip().split('\n')
        
        # Extract window indices from non-comment lines
        window_indices = []
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            # Parse line format: "1. process | type | title"
            if '. ' in line:
                try:
                    index_part = line.split('.', 1)[0].strip()
                    original_index = int(index_part) - 1  # Convert to 0-based
                    if 0 <= original_index < len(original_windows):
                        window_indices.append(original_index)
                except (ValueError, IndexError):
                    continue
        
        # Reorder windows based on parsed indices
        reordered_windows = []
        for index in window_indices:
            if index < len(original_windows):
                reordered_windows.append(original_windows[index])
        
        # Add any missing windows at the end
        for i, window in enumerate(original_windows):
            if i not in window_indices:
                reordered_windows.append(window)
        
        return reordered_windows


class SimpleWindowOrganizer:
    """Main class that coordinates export/import workflow."""
    
    def __init__(self, work_dir=None):
        self.work_dir = work_dir or os.getcwd()
        self.file_path = os.path.join(self.work_dir, "window_order.txt")
        self.exporter = WindowExporter(self.file_path)
        self.importer = WindowImporter(self.file_path)
    
    def export_for_editing(self, windows: List, group_name: str = "Windows") -> str:
        """Export windows to file for manual editing."""
        return self.exporter.export_windows(windows, group_name)
    
    def import_edited_order(self, original_windows: List) -> List:
        """Import the edited window order."""
        return self.importer.import_window_order(original_windows)
    
    def has_edited_file(self) -> bool:
        """Check if an edited file exists."""
        return os.path.exists(self.file_path)
    
    def cleanup(self):
        """Remove the temporary ordering file."""
        if os.path.exists(self.file_path):
            try:
                os.remove(self.file_path)
            except OSError:
                pass


# Convenience functions for easy integration
def export_windows_for_editing(windows: List, group_name: str = "Windows", work_dir=None) -> str:
    """
    Export windows to a file for manual editing.
    
    Args:
        windows: List of Window objects
        group_name: Name of the window group
        work_dir: Working directory (defaults to current directory)
        
    Returns:
        Path to the exported file
    """
    organizer = SimpleWindowOrganizer(work_dir)
    return organizer.export_for_editing(windows, group_name)


def import_edited_window_order(original_windows: List, work_dir=None) -> List:
    """
    Import the edited window order.
    
    Args:
        original_windows: Original list of Window objects
        work_dir: Working directory (defaults to current directory)
        
    Returns:
        Reordered list of Window objects
    """
    organizer = SimpleWindowOrganizer(work_dir)
    return organizer.import_edited_order(original_windows)


def has_window_order_file(work_dir=None) -> bool:
    """Check if a window order file exists for importing."""
    organizer = SimpleWindowOrganizer(work_dir)
    return organizer.has_edited_file()


def cleanup_window_order_file(work_dir=None):
    """Clean up the temporary window order file."""
    organizer = SimpleWindowOrganizer(work_dir)
    organizer.cleanup()


# Example usage and testing
if __name__ == "__main__":
    # Mock Window class for testing
    class MockWindow:
        def __init__(self, title, process_name, window_type="Unknown"):
            self.title = title
            self.process_name = process_name
            self.type = window_type
    
    # Test with mock windows
    test_windows = [
        MockWindow("Downloads - File Explorer", "explorer.exe", "Explorer"),
        MockWindow("Desktop - File Explorer", "explorer.exe", "Explorer"),
        MockWindow("Documents - File Explorer", "explorer.exe", "Explorer"),
        MockWindow("GitHub - Chrome", "chrome.exe", "Browser"),
        MockWindow("Stack Overflow - Chrome", "chrome.exe", "Browser"),
    ]
    
    print("Testing Simple Window Organizer")
    print("=" * 40)
    
    # Export
    file_path = export_windows_for_editing(test_windows, "Test Explorer Windows")
    print(f"✅ Exported to: {file_path}")
    
    # Show content
    with open(file_path, 'r') as f:
        content = f.read()
    print("\nExported content:")
    print("-" * 20)
    print(content)
    
    # Test import (without editing)
    imported = import_edited_window_order(test_windows)
    print(f"\n✅ Imported {len(imported)} windows")
    
    # Cleanup
    cleanup_window_order_file()
    print("✅ Cleaned up temporary file")
