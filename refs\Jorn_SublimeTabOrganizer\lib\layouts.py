import sublime
import json
import os

# Default layouts based on the examples provided
DEFAULT_LAYOUTS = {
    "single": {
        "cells": [[0, 0, 1, 1]],
        "cols": [0.0, 1.0],
        "rows": [0.0, 1.0],
        "nickname": "Single Pane"
    },
    "columns_2": {
        "cells": [[0, 0, 1, 1], [1, 0, 2, 1]],
        "cols": [0.0, 0.5, 1.0],
        "rows": [0.0, 1.0],
        "nickname": "Two Columns"
    },
    "columns_3": {
        "cells": [[0, 0, 1, 1], [1, 0, 2, 1], [2, 0, 3, 1]],
        "cols": [0.0, 0.33, 0.66, 1.0],
        "rows": [0.0, 1.0],
        "nickname": "Three Columns"
    },
    "rows_2": {
        "cells": [[0, 0, 1, 1], [0, 1, 1, 2]],
        "cols": [0.0, 1.0],
        "rows": [0.0, 0.5, 1.0],
        "nickname": "Two Rows"
    },
    "rows_3": {
        "cells": [[0, 0, 1, 1], [0, 1, 1, 2], [0, 2, 1, 3]],
        "cols": [0.0, 1.0],
        "rows": [0.0, 0.33, 0.66, 1.0],
        "nickname": "Three Rows"
    },
    "grid_2x2": {
        "cells": [[0, 0, 1, 1], [1, 0, 2, 1], [0, 1, 1, 2], [1, 1, 2, 2]],
        "cols": [0.0, 0.5, 1.0],
        "rows": [0.0, 0.5, 1.0],
        "nickname": "2x2 Grid"
    },
    "two_rows_lower_two_columns": {
        "cells": [[0, 0, 2, 1], [0, 1, 1, 2], [1, 1, 2, 2]],
        "cols": [0.0, 0.5, 1.0],
        "rows": [0.0, 0.5, 1.0],
        "nickname": "Two Rows, Lower Row has Two Columns"
    },
    "two_rows_upper_two_columns": {
        "cells": [[0, 0, 1, 1], [1, 0, 2, 1], [0, 1, 2, 2]],
        "cols": [0.0, 0.5, 1.0],
        "rows": [0.0, 0.5, 1.0],
        "nickname": "Two Rows, Upper Row has Two Columns"
    },
    "three_rows_lower_two_columns": {
        "cells": [[0, 0, 2, 1], [0, 1, 2, 2], [0, 2, 1, 3], [1, 2, 2, 3]],
        "cols": [0.0, 0.5, 1.0],
        "rows": [0.0, 0.33, 0.66, 1.0],
        "nickname": "Three Rows, Lower Row has Two Columns"
    }
}

class LayoutManager:
    """Manages layout templates for tab organization"""
    
    def __init__(self):
        self.layouts = DEFAULT_LAYOUTS.copy()
        self.load_custom_layouts()
    
    def load_custom_layouts(self):
        """Load custom layouts from settings file"""
        try:
            settings = sublime.load_settings('JornTabCategorizer.sublime-settings')
            custom_layouts = settings.get('custom_layouts', {})
            self.layouts.update(custom_layouts)
        except Exception as e:
            print(f"[JornTabCategorizer] Error loading custom layouts: {e}")
    
    def get_layout(self, layout_name):
        """Get a layout by name"""
        return self.layouts.get(layout_name)
    
    def get_layout_names(self):
        """Get a list of all available layout names"""
        return sorted(self.layouts.keys())
    
    def apply_layout(self, window, layout_name):
        """Apply a layout to a window"""
        layout = self.get_layout(layout_name)
        if not layout:
            print(f"[JornTabCategorizer] Layout '{layout_name}' not found")
            return False
        
        try:
            # Extract the layout components
            cells = layout.get('cells', [])
            cols = layout.get('cols', [0.0, 1.0])
            rows = layout.get('rows', [0.0, 1.0])
            
            # Create the layout dictionary
            layout_dict = {
                "cells": cells,
                "cols": cols,
                "rows": rows
            }
            
            # Apply the layout to the window
            window.set_layout(layout_dict)
            print(f"[JornTabCategorizer] Applied layout '{layout_name}'")
            return True
        except Exception as e:
            print(f"[JornTabCategorizer] Error applying layout '{layout_name}': {e}")
            return False
    
    def get_cell_count(self, layout_name):
        """Get the number of cells in a layout"""
        layout = self.get_layout(layout_name)
        if not layout:
            return 0
        return len(layout.get('cells', []))

# Initialize the layout manager
layout_manager = LayoutManager()
