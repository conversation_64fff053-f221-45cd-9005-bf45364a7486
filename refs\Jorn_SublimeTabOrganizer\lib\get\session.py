import datetime

class TabSessionManager:
    """Singleton class to manage tab session data across the plugin"""
    _instance = None
    
    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = TabSessionManager()
        return cls._instance
    
    def __init__(self):
        # Initialize session data storage
        self.session_data = {}  # Key: view_id, Value: metrics dictionary
        
    def get_tab_metrics(self, view_id):
        """Get metrics for a specific tab, creating entry if needed"""
        if view_id not in self.session_data:
            self.session_data[view_id] = {
                'save_count': 0,
                'last_save_time': None,
                'first_seen': datetime.datetime.now(),
                'activation_count': 0,
                'last_activated': datetime.datetime.now()
            }
        return self.session_data[view_id]
    
    def record_save(self, view_id):
        """Record a save event for a tab"""
        metrics = self.get_tab_metrics(view_id)
        metrics['save_count'] += 1
        metrics['last_save_time'] = datetime.datetime.now()
        
    def record_activation(self, view_id):
        """Record an activation event for a tab"""
        metrics = self.get_tab_metrics(view_id)
        metrics['activation_count'] += 1
        metrics['last_activated'] = datetime.datetime.now()
        
    def get_time_since_save(self, view_id):
        """Get formatted time since last save"""
        metrics = self.get_tab_metrics(view_id)
        if metrics['last_save_time'] is None:
            return "D : unsaved"
            
        delta = datetime.datetime.now() - metrics['last_save_time']
        minutes = delta.total_seconds() / 60
        
        if minutes < 5:
            return "0 : <5 min"
        elif minutes < 15:
            return "A : <15 min"
        elif minutes < 60:
            return "B : <1 hour"
        else:
            return "C : +hour"
    
    def get_time_since_save_minutes(self, view_id):
        """Get time since last save in minutes for sorting"""
        metrics = self.get_tab_metrics(view_id)
        if metrics['last_save_time'] is None:
            return float('inf')  # Unsaved files come last
            
        delta = datetime.datetime.now() - metrics['last_save_time']
        return delta.total_seconds() / 60
    
    def calculate_activity_score(self, view_id):
        """Calculate an activity score based on saves and activations"""
        metrics = self.get_tab_metrics(view_id)
        
        # Weights for different metrics
        save_weight = 2
        activation_weight = 1
        time_factor = 1.0
        
        # Adjust time factor based on recency
        if metrics.get('last_save_time'):
            delta = datetime.datetime.now() - metrics['last_save_time']
            hours = delta.total_seconds() / 3600
            if hours < 1:
                time_factor = 2.0  # Boost recent saves
            elif hours > 24:
                time_factor = 0.5  # Reduce old saves
        
        # Calculate activity score
        return (
            metrics.get('save_count', 0) * save_weight + 
            metrics.get('activation_count', 0) * activation_weight
        ) * time_factor
