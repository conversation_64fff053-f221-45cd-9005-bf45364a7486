#!/usr/bin/env python3
"""
Test script for Window Ordering Rules functionality.

This script tests the window ordering system to ensure it works correctly
with different window types, especially Explorer windows.
"""

import sys
import os

# Add the src directory to the path so we can import window_tiler
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import the ordering system
try:
    from window_ordering_rules import (
        ExplorerWindowOrderer, GeneralWindowOrderer, 
        WindowOrderingRule, OrderingCriteria, SortDirection,
        get_orderer_for_window_type, create_explorer_default_rules
    )
    ORDERING_AVAILABLE = True
except ImportError as e:
    print(f"❌ Failed to import ordering rules: {e}")
    ORDERING_AVAILABLE = False
    sys.exit(1)

# Mock Window class for testing
class MockWindow:
    """Mock window class for testing ordering functionality."""
    
    def __init__(self, hwnd, title, process_name, window_type):
        self.hwnd = hwnd
        self.title = title
        self.process_name = process_name
        self.type = window_type
    
    def __repr__(self):
        return f"<MockWindow: {self.title}>"


def test_explorer_ordering():
    """Test Explorer window ordering functionality."""
    print("\n🗂️  Testing Explorer Window Ordering")
    print("=" * 50)
    
    # Create mock Explorer windows with various folder types
    explorer_windows = [
        MockWindow(1, "Downloads - File Explorer", "explorer.exe", "Explorer"),
        MockWindow(2, "C:\\Users\\<USER>\\Desktop - File Explorer", "explorer.exe", "Explorer"),
        MockWindow(3, "Documents - File Explorer", "explorer.exe", "Explorer"),
        MockWindow(4, "C:\\Program Files - File Explorer", "explorer.exe", "Explorer"),
        MockWindow(5, "Pictures - File Explorer", "explorer.exe", "Explorer"),
        MockWindow(6, "D:\\Projects\\MyProject - File Explorer", "explorer.exe", "Explorer"),
        MockWindow(7, "This PC", "explorer.exe", "Explorer"),
        MockWindow(8, "C:\\Users\\<USER>\\Documents\\Work\\Reports - File Explorer", "explorer.exe", "Explorer"),
    ]
    
    print("Original order:")
    for i, window in enumerate(explorer_windows, 1):
        print(f"  {i}. {window.title}")
    
    # Test with default Explorer rules
    orderer = get_orderer_for_window_type('Explorer')
    ordered_windows = orderer.order_windows(explorer_windows)
    
    print("\nOrdered by default Explorer rules:")
    for i, window in enumerate(ordered_windows, 1):
        print(f"  {i}. {window.title}")
    
    # Verify expected ordering patterns
    titles = [w.title for w in ordered_windows]
    
    # Check that special folders come first
    special_folders = ['Desktop', 'Documents', 'Downloads', 'Pictures', 'This PC']
    special_found = []
    for title in titles:
        for folder in special_folders:
            if folder in title and folder not in special_found:
                special_found.append(folder)
                break
    
    print(f"\n✅ Special folders found in order: {special_found}")
    
    # Check that C: drive comes before D: drive
    c_drive_pos = next((i for i, title in enumerate(titles) if 'C:' in title), -1)
    d_drive_pos = next((i for i, title in enumerate(titles) if 'D:' in title), -1)
    
    if c_drive_pos != -1 and d_drive_pos != -1:
        if c_drive_pos < d_drive_pos:
            print("✅ C: drive comes before D: drive")
        else:
            print("❌ Drive ordering incorrect")
    
    return True


def test_general_ordering():
    """Test general window ordering functionality."""
    print("\n🌐 Testing General Window Ordering")
    print("=" * 50)
    
    # Create mock browser windows
    browser_windows = [
        MockWindow(1, "GitHub - Google Chrome", "chrome.exe", "Browser"),
        MockWindow(2, "Stack Overflow - Google Chrome", "chrome.exe", "Browser"),
        MockWindow(3, "Documentation - Google Chrome", "chrome.exe", "Browser"),
        MockWindow(4, "Amazon - Google Chrome", "chrome.exe", "Browser"),
    ]
    
    print("Original order:")
    for i, window in enumerate(browser_windows, 1):
        print(f"  {i}. {window.title}")
    
    # Test with default Browser rules (alphabetical)
    orderer = get_orderer_for_window_type('Browser')
    ordered_windows = orderer.order_windows(browser_windows)
    
    print("\nOrdered alphabetically:")
    for i, window in enumerate(ordered_windows, 1):
        print(f"  {i}. {window.title}")
    
    # Verify alphabetical ordering
    titles = [w.title for w in ordered_windows]
    is_alphabetical = titles == sorted(titles, key=str.lower)
    
    if is_alphabetical:
        print("✅ Windows are correctly ordered alphabetically")
    else:
        print("❌ Alphabetical ordering failed")
    
    return is_alphabetical


def test_custom_rules():
    """Test custom ordering rules."""
    print("\n⚙️  Testing Custom Ordering Rules")
    print("=" * 50)
    
    # Create a custom orderer with specific rules
    custom_rules = [
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_CONTAINS,
            direction=SortDirection.ASCENDING,
            priority=1,
            parameters={'search_term': 'GitHub'}
        ),
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_ALPHABETICAL,
            direction=SortDirection.ASCENDING,
            priority=2
        )
    ]
    
    orderer = GeneralWindowOrderer(custom_rules)
    
    # Test windows
    test_windows = [
        MockWindow(1, "Stack Overflow - Chrome", "chrome.exe", "Browser"),
        MockWindow(2, "GitHub Repository - Chrome", "chrome.exe", "Browser"),
        MockWindow(3, "Amazon Shopping - Chrome", "chrome.exe", "Browser"),
        MockWindow(4, "GitHub Issues - Chrome", "chrome.exe", "Browser"),
    ]
    
    print("Original order:")
    for i, window in enumerate(test_windows, 1):
        print(f"  {i}. {window.title}")
    
    ordered_windows = orderer.order_windows(test_windows)
    
    print("\nOrdered by custom rules (GitHub first, then alphabetical):")
    for i, window in enumerate(ordered_windows, 1):
        print(f"  {i}. {window.title}")
    
    # Verify GitHub windows come first
    titles = [w.title for w in ordered_windows]
    github_positions = [i for i, title in enumerate(titles) if 'GitHub' in title]
    non_github_positions = [i for i, title in enumerate(titles) if 'GitHub' not in title]
    
    github_first = all(g < n for g in github_positions for n in non_github_positions)
    
    if github_first:
        print("✅ GitHub windows correctly prioritized")
    else:
        print("❌ Custom rule prioritization failed")
    
    return github_first


def test_integration_with_window_tiler():
    """Test integration with the main window tiler."""
    print("\n🔗 Testing Integration with Window Tiler")
    print("=" * 50)
    
    try:
        from window_tiler import WindowManager, Config
        
        # Create a window manager
        config = Config()
        window_manager = WindowManager(config)
        
        # Check if ordering is available
        if hasattr(window_manager, 'ordering_enabled'):
            print(f"✅ WindowManager has ordering_enabled: {window_manager.ordering_enabled}")
        else:
            print("❌ WindowManager missing ordering_enabled attribute")
            return False
        
        # Check if ordering methods exist
        required_methods = [
            'order_windows_in_group',
            'get_windows_by_process_ordered',
            'get_windows_by_type_ordered',
            'get_all_windows_as_group_ordered'
        ]
        
        for method in required_methods:
            if hasattr(window_manager, method):
                print(f"✅ WindowManager has method: {method}")
            else:
                print(f"❌ WindowManager missing method: {method}")
                return False
        
        print("✅ All integration points are properly implemented")
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import window_tiler: {e}")
        return False


def main():
    """Run all tests."""
    print("Window Ordering Rules Test Suite")
    print("=" * 60)
    
    if not ORDERING_AVAILABLE:
        print("❌ Window ordering system not available")
        return False
    
    print("✅ Window ordering system imported successfully")
    
    # Run all tests
    tests = [
        test_explorer_ordering,
        test_general_ordering,
        test_custom_rules,
        test_integration_with_window_tiler
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with error: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 ALL TESTS PASSED! ({passed}/{total})")
        print("\n✨ Window ordering functionality is working correctly!")
        print("\nTo use window ordering in the main application:")
        print("1. Run: python src/window_tiler.py")
        print("2. Choose 'Tile windows'")
        print("3. When prompted, enable 'intelligent window ordering'")
        print("4. Explorer windows will be ordered by special folders, drives, and paths")
        print("5. Other windows will be ordered alphabetically")
        return True
    else:
        print(f"❌ {total - passed} TESTS FAILED ({passed}/{total} passed)")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
