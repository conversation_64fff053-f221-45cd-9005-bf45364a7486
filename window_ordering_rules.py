#!/usr/bin/env python3
"""
Window Ordering Rules Configuration

This file defines flexible rules for ordering windows within groups before tiling.
Users can modify these rules to customize how windows are arranged in the grid.

The system supports different rule sets for different window types, allowing
fine-grained control over window positioning.
"""

import re
import os
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Callable, Optional
from enum import Enum, auto


class OrderingCriteria(Enum):
    """Available criteria for ordering windows."""
    TITLE_ALPHABETICAL = auto()
    TITLE_LENGTH = auto()
    TITLE_CONTAINS = auto()
    TITLE_REGEX = auto()
    PROCESS_NAME = auto()
    WINDOW_TYPE = auto()
    CUSTOM_FUNCTION = auto()
    # Explorer-specific criteria
    EXPLORER_PATH_DEPTH = auto()
    EXPLORER_SPECIAL_FOLDERS = auto()
    EXPLORER_DRIVE_ORDER = auto()


class SortDirection(Enum):
    """Sort direction for ordering rules."""
    ASCENDING = auto()
    DESCENDING = auto()


class WindowOrderingRule:
    """
    Defines a single ordering rule with criteria, direction, and optional parameters.
    """
    def __init__(self, 
                 criteria: OrderingCriteria, 
                 direction: SortDirection = SortDirection.ASCENDING,
                 priority: int = 1,
                 parameters: Optional[Dict[str, Any]] = None,
                 custom_function: Optional[Callable] = None):
        self.criteria = criteria
        self.direction = direction
        self.priority = priority  # Lower numbers = higher priority
        self.parameters = parameters or {}
        self.custom_function = custom_function

    def __repr__(self):
        return f"<Rule: {self.criteria.name} {self.direction.name} (priority={self.priority})>"


class BaseWindowOrderer(ABC):
    """
    Abstract base class for window ordering strategies.
    Each window type can have its own orderer implementation.
    """
    
    def __init__(self, rules: List[WindowOrderingRule] = None):
        self.rules = rules or []
    
    @abstractmethod
    def get_sort_key(self, window) -> tuple:
        """
        Generate a sort key for the given window based on the configured rules.
        Returns a tuple that can be used with Python's sorted() function.
        """
        pass
    
    def order_windows(self, windows: List) -> List:
        """
        Order the given list of windows according to the configured rules.
        """
        if not self.rules:
            return windows
        
        return sorted(windows, key=self.get_sort_key)
    
    def add_rule(self, rule: WindowOrderingRule):
        """Add a new ordering rule."""
        self.rules.append(rule)
        # Sort rules by priority
        self.rules.sort(key=lambda r: r.priority)
    
    def clear_rules(self):
        """Clear all ordering rules."""
        self.rules.clear()


class ExplorerWindowOrderer(BaseWindowOrderer):
    """
    Specialized orderer for Windows Explorer windows.
    Provides intelligent ordering based on folder paths, special folders, and drives.
    """
    
    # Special folder priorities (lower number = higher priority)
    SPECIAL_FOLDER_PRIORITIES = {
        'Desktop': 1,
        'Documents': 2,
        'Downloads': 3,
        'Pictures': 4,
        'Music': 5,
        'Videos': 6,
        'This PC': 7,
        'Computer': 7,
        'My Computer': 7,
    }
    
    # Drive order priorities
    DRIVE_PRIORITIES = {
        'C:': 1,
        'D:': 2,
        'E:': 3,
        'F:': 4,
    }
    
    def get_sort_key(self, window) -> tuple:
        """Generate sort key for Explorer windows."""
        sort_components = []
        
        for rule in self.rules:
            component = self._get_rule_component(window, rule)
            if rule.direction == SortDirection.DESCENDING:
                # For descending order, negate numeric values or reverse strings
                if isinstance(component, (int, float)):
                    component = -component
                elif isinstance(component, str):
                    component = component[::-1]  # Reverse string for descending sort
            
            sort_components.append(component)
        
        return tuple(sort_components)
    
    def _get_rule_component(self, window, rule: WindowOrderingRule):
        """Get the sort component for a specific rule."""
        title = window.title
        
        if rule.criteria == OrderingCriteria.TITLE_ALPHABETICAL:
            return title.lower()
        
        elif rule.criteria == OrderingCriteria.TITLE_LENGTH:
            return len(title)
        
        elif rule.criteria == OrderingCriteria.TITLE_CONTAINS:
            search_term = rule.parameters.get('search_term', '')
            return 0 if search_term.lower() in title.lower() else 1
        
        elif rule.criteria == OrderingCriteria.TITLE_REGEX:
            pattern = rule.parameters.get('pattern', '')
            return 0 if re.search(pattern, title, re.IGNORECASE) else 1
        
        elif rule.criteria == OrderingCriteria.EXPLORER_SPECIAL_FOLDERS:
            return self._get_special_folder_priority(title)
        
        elif rule.criteria == OrderingCriteria.EXPLORER_PATH_DEPTH:
            return self._get_path_depth(title)
        
        elif rule.criteria == OrderingCriteria.EXPLORER_DRIVE_ORDER:
            return self._get_drive_priority(title)
        
        elif rule.criteria == OrderingCriteria.CUSTOM_FUNCTION and rule.custom_function:
            return rule.custom_function(window)
        
        else:
            return title.lower()  # Default fallback
    
    def _get_special_folder_priority(self, title: str) -> int:
        """Get priority for special folders."""
        for folder_name, priority in self.SPECIAL_FOLDER_PRIORITIES.items():
            if folder_name.lower() in title.lower():
                return priority
        return 999  # Unknown folders go last
    
    def _get_path_depth(self, title: str) -> int:
        """Calculate path depth based on backslashes in title."""
        # Count backslashes to estimate folder depth
        return title.count('\\') + title.count('/')
    
    def _get_drive_priority(self, title: str) -> int:
        """Get drive priority from window title."""
        for drive, priority in self.DRIVE_PRIORITIES.items():
            if drive in title:
                return priority
        return 999  # Unknown drives go last


class GeneralWindowOrderer(BaseWindowOrderer):
    """
    General-purpose orderer for non-Explorer windows.
    Handles browsers, terminals, editors, etc.
    """
    
    def get_sort_key(self, window) -> tuple:
        """Generate sort key for general windows."""
        sort_components = []
        
        for rule in self.rules:
            component = self._get_rule_component(window, rule)
            if rule.direction == SortDirection.DESCENDING:
                if isinstance(component, (int, float)):
                    component = -component
                elif isinstance(component, str):
                    component = component[::-1]
            
            sort_components.append(component)
        
        return tuple(sort_components)
    
    def _get_rule_component(self, window, rule: WindowOrderingRule):
        """Get the sort component for a specific rule."""
        title = window.title
        
        if rule.criteria == OrderingCriteria.TITLE_ALPHABETICAL:
            return title.lower()
        
        elif rule.criteria == OrderingCriteria.TITLE_LENGTH:
            return len(title)
        
        elif rule.criteria == OrderingCriteria.TITLE_CONTAINS:
            search_term = rule.parameters.get('search_term', '')
            return 0 if search_term.lower() in title.lower() else 1
        
        elif rule.criteria == OrderingCriteria.TITLE_REGEX:
            pattern = rule.parameters.get('pattern', '')
            return 0 if re.search(pattern, title, re.IGNORECASE) else 1
        
        elif rule.criteria == OrderingCriteria.PROCESS_NAME:
            return window.process_name.lower()
        
        elif rule.criteria == OrderingCriteria.WINDOW_TYPE:
            return str(window.type).lower()
        
        elif rule.criteria == OrderingCriteria.CUSTOM_FUNCTION and rule.custom_function:
            return rule.custom_function(window)
        
        else:
            return title.lower()  # Default fallback


# =============================================================================
# PREDEFINED RULE SETS
# =============================================================================

def create_explorer_default_rules() -> List[WindowOrderingRule]:
    """Create default ordering rules for Explorer windows."""
    return [
        # 1. Special folders first (Desktop, Documents, etc.)
        WindowOrderingRule(
            criteria=OrderingCriteria.EXPLORER_SPECIAL_FOLDERS,
            direction=SortDirection.ASCENDING,
            priority=1
        ),
        # 2. Then by drive order (C:, D:, etc.)
        WindowOrderingRule(
            criteria=OrderingCriteria.EXPLORER_DRIVE_ORDER,
            direction=SortDirection.ASCENDING,
            priority=2
        ),
        # 3. Then by path depth (shallower paths first)
        WindowOrderingRule(
            criteria=OrderingCriteria.EXPLORER_PATH_DEPTH,
            direction=SortDirection.ASCENDING,
            priority=3
        ),
        # 4. Finally alphabetical by title
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_ALPHABETICAL,
            direction=SortDirection.ASCENDING,
            priority=4
        )
    ]


def create_browser_default_rules() -> List[WindowOrderingRule]:
    """Create default ordering rules for browser windows."""
    return [
        # Order browser windows alphabetically by title
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_ALPHABETICAL,
            direction=SortDirection.ASCENDING,
            priority=1
        )
    ]


def create_general_default_rules() -> List[WindowOrderingRule]:
    """Create default ordering rules for general windows."""
    return [
        # Order general windows alphabetically by title
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_ALPHABETICAL,
            direction=SortDirection.ASCENDING,
            priority=1
        )
    ]


# =============================================================================
# RULE SET REGISTRY
# =============================================================================

DEFAULT_RULE_SETS = {
    'Explorer': create_explorer_default_rules,
    'Browser': create_browser_default_rules,
    'Terminal': create_general_default_rules,
    'Editor': create_general_default_rules,
    'IDE': create_general_default_rules,
    'Other': create_general_default_rules,
}


def get_orderer_for_window_type(window_type: str) -> BaseWindowOrderer:
    """
    Get the appropriate orderer for the given window type.
    
    Args:
        window_type: The window type string (e.g., 'Explorer', 'Browser', etc.)
    
    Returns:
        Configured window orderer instance
    """
    if window_type == 'Explorer':
        rules = create_explorer_default_rules()
        return ExplorerWindowOrderer(rules)
    else:
        rule_factory = DEFAULT_RULE_SETS.get(window_type, create_general_default_rules)
        rules = rule_factory()
        return GeneralWindowOrderer(rules)


# =============================================================================
# EXAMPLE CUSTOM RULES
# =============================================================================

def create_custom_explorer_rules() -> List[WindowOrderingRule]:
    """
    Example of custom Explorer ordering rules.
    Users can modify this function to create their own ordering preferences.
    """
    return [
        # Custom rule: Downloads folder always first
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_CONTAINS,
            direction=SortDirection.ASCENDING,
            priority=1,
            parameters={'search_term': 'Downloads'}
        ),
        # Then Desktop
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_CONTAINS,
            direction=SortDirection.ASCENDING,
            priority=2,
            parameters={'search_term': 'Desktop'}
        ),
        # Then everything else alphabetically
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_ALPHABETICAL,
            direction=SortDirection.ASCENDING,
            priority=3
        )
    ]


def create_custom_browser_rules() -> List[WindowOrderingRule]:
    """
    Example of custom browser ordering rules.
    """
    return [
        # GitHub tabs first
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_CONTAINS,
            direction=SortDirection.ASCENDING,
            priority=1,
            parameters={'search_term': 'GitHub'}
        ),
        # Then documentation sites
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_REGEX,
            direction=SortDirection.ASCENDING,
            priority=2,
            parameters={'pattern': r'(docs|documentation|manual)'}
        ),
        # Everything else alphabetically
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_ALPHABETICAL,
            direction=SortDirection.ASCENDING,
            priority=3
        )
    ]
