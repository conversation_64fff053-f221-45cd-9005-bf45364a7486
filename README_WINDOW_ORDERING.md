# Window Ordering Rules System

## 🎯 Overview

The Window Tiler now includes an **intelligent window ordering system** that automatically arranges windows within groups according to customizable rules. This feature provides smart, context-aware ordering that makes your tiled layouts more logical and productive.

## ✨ Key Features

- **🗂️ Explorer-Specific Rules**: Special folders (Desktop, Documents, Downloads) prioritized, drive ordering (C:, D:, etc.), path depth awareness
- **🌐 General Window Rules**: Alphabetical ordering, custom search patterns, regex matching
- **⚙️ Flexible Rule System**: Priority-based rules, multiple criteria per window type, easy customization
- **🔗 Seamless Integration**: Optional feature that enhances existing workflow without disruption

## 🚀 Quick Start

### Basic Usage

1. **Run Window Tiler**: `python src/window_tiler.py`
2. **Choose "Tile windows"**
3. **Enable ordering**: When prompted, choose "Yes" for intelligent window ordering
4. **Select grouping**: Choose process name or window type grouping
5. **Pick your group**: Windows will be automatically ordered within the selected group

### What You'll See

**Before Ordering** (random):
```
1. D:\Projects\MyProject - File Explorer
2. Downloads - File Explorer  
3. C:\Program Files - File Explorer
4. Desktop - File Explorer
```

**After Ordering** (intelligent):
```
1. Desktop - File Explorer          # Special folder first
2. Downloads - File Explorer        # Then other special folders
3. C:\Program Files - File Explorer # C: drive before D: drive
4. D:\Projects\MyProject - File Explorer
```

## 🏗️ Architecture

### Rule-Based System

The ordering system uses **priority-based rules** that can be combined and customized:

```python
WindowOrderingRule(
    criteria=OrderingCriteria.EXPLORER_SPECIAL_FOLDERS,
    direction=SortDirection.ASCENDING,
    priority=1  # Lower number = higher priority
)
```

### Window Type Specialization

Different window types use different ordering strategies:

- **Explorer**: Special folders → Drive order → Path depth → Alphabetical
- **Browser**: Alphabetical by title (customizable for GitHub, docs, etc.)
- **Terminal/Editor/IDE**: Alphabetical by title
- **General**: Alphabetical by title

## 📋 Available Ordering Criteria

### General Criteria
- `TITLE_ALPHABETICAL`: Sort by window title alphabetically
- `TITLE_LENGTH`: Sort by title length
- `TITLE_CONTAINS`: Prioritize windows containing specific text
- `TITLE_REGEX`: Use regex patterns for matching
- `PROCESS_NAME`: Sort by executable name
- `WINDOW_TYPE`: Sort by window type category

### Explorer-Specific Criteria
- `EXPLORER_SPECIAL_FOLDERS`: Prioritize Desktop, Documents, Downloads, etc.
- `EXPLORER_DRIVE_ORDER`: Order by drive letter (C:, D:, E:, etc.)
- `EXPLORER_PATH_DEPTH`: Shallow paths before deep paths

### Custom Criteria
- `CUSTOM_FUNCTION`: Use your own Python function for ordering

## 🛠️ Customization Guide

### Modifying Default Rules

Edit `window_ordering_rules.py` to customize the default behavior:

```python
def create_explorer_default_rules() -> List[WindowOrderingRule]:
    return [
        # 1. Downloads folder always first (custom priority)
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_CONTAINS,
            direction=SortDirection.ASCENDING,
            priority=1,
            parameters={'search_term': 'Downloads'}
        ),
        # 2. Then other special folders
        WindowOrderingRule(
            criteria=OrderingCriteria.EXPLORER_SPECIAL_FOLDERS,
            direction=SortDirection.ASCENDING,
            priority=2
        ),
        # 3. Then alphabetical
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_ALPHABETICAL,
            direction=SortDirection.ASCENDING,
            priority=3
        )
    ]
```

### Creating Custom Browser Rules

```python
def create_custom_browser_rules() -> List[WindowOrderingRule]:
    return [
        # GitHub tabs first
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_CONTAINS,
            direction=SortDirection.ASCENDING,
            priority=1,
            parameters={'search_term': 'GitHub'}
        ),
        # Documentation sites second
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_REGEX,
            direction=SortDirection.ASCENDING,
            priority=2,
            parameters={'pattern': r'(docs|documentation|manual)'}
        ),
        # Everything else alphabetically
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_ALPHABETICAL,
            direction=SortDirection.ASCENDING,
            priority=3
        )
    ]
```

### Adding Custom Functions

```python
def my_custom_priority(window):
    """Custom function to prioritize certain windows."""
    title = window.title.lower()
    if 'urgent' in title:
        return 0  # Highest priority
    elif 'work' in title:
        return 1  # Medium priority
    else:
        return 2  # Lowest priority

# Use in a rule
WindowOrderingRule(
    criteria=OrderingCriteria.CUSTOM_FUNCTION,
    direction=SortDirection.ASCENDING,
    priority=1,
    custom_function=my_custom_priority
)
```

## 🔧 Configuration Examples

### Example 1: Developer Workflow
```python
# Prioritize development-related windows
def create_developer_rules():
    return [
        # Code editors first
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_CONTAINS,
            priority=1,
            parameters={'search_term': 'Visual Studio Code'}
        ),
        # Then terminals
        WindowOrderingRule(
            criteria=OrderingCriteria.WINDOW_TYPE,
            priority=2,
            parameters={'window_type': 'Terminal'}
        ),
        # Then browsers with documentation
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_REGEX,
            priority=3,
            parameters={'pattern': r'(docs|stackoverflow|github)'}
        )
    ]
```

### Example 2: Media Management
```python
# Organize media-related Explorer windows
def create_media_explorer_rules():
    return [
        # Pictures folder first
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_CONTAINS,
            priority=1,
            parameters={'search_term': 'Pictures'}
        ),
        # Videos folder second
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_CONTAINS,
            priority=2,
            parameters={'search_term': 'Videos'}
        ),
        # Music folder third
        WindowOrderingRule(
            criteria=OrderingCriteria.TITLE_CONTAINS,
            priority=3,
            parameters={'search_term': 'Music'}
        ),
        # Everything else by path depth
        WindowOrderingRule(
            criteria=OrderingCriteria.EXPLORER_PATH_DEPTH,
            priority=4
        )
    ]
```

## 🧪 Testing Your Rules

Run the test suite to validate your custom rules:

```bash
python test_window_ordering.py
```

The test will verify:
- ✅ Explorer window ordering (special folders, drives, paths)
- ✅ General window alphabetical ordering
- ✅ Custom rule prioritization
- ✅ Integration with main Window Tiler

## 🎛️ Advanced Features

### Rule Priorities

Rules with lower priority numbers execute first:
- Priority 1: Most important (executes first)
- Priority 2: Secondary sorting
- Priority 3+: Additional refinement

### Sort Directions

- `SortDirection.ASCENDING`: A→Z, 1→9, shallow→deep
- `SortDirection.DESCENDING`: Z→A, 9→1, deep→shallow

### Combining Multiple Rules

Rules are applied in priority order, creating a **tuple sort key**:
```python
# Results in sort key: (special_folder_priority, drive_priority, alphabetical)
(1, 1, "desktop")  # Desktop on C: drive
(2, 1, "documents")  # Documents on C: drive  
(1, 2, "desktop")  # Desktop on D: drive
```

## 🚨 Troubleshooting

### Ordering Not Working?
1. Check that `window_ordering_rules.py` is in the same directory as `window_tiler.py`
2. Verify no syntax errors in your custom rules
3. Run `python test_window_ordering.py` to validate the system

### Custom Rules Not Applied?
1. Ensure your custom rule functions are called in `DEFAULT_RULE_SETS`
2. Check rule priorities (lower numbers = higher priority)
3. Verify search terms and regex patterns are correct

### Performance Issues?
1. Limit the number of rules per window type
2. Use simple criteria before complex ones
3. Avoid expensive custom functions for large window counts

## 🎯 Best Practices

1. **Start Simple**: Begin with default rules, then customize gradually
2. **Test Thoroughly**: Use the test suite when making changes
3. **Priority Planning**: Design your rule priorities before implementation
4. **Backup Rules**: Keep a copy of working rules before major changes
5. **Performance First**: Simple rules perform better than complex ones

## 🔮 Future Enhancements

The ordering system is designed for extensibility. Potential future features:
- **Saved Rule Profiles**: Multiple rule sets for different workflows
- **Time-Based Rules**: Different ordering based on time of day
- **Window Size Awareness**: Consider window dimensions in ordering
- **User Learning**: Adapt rules based on usage patterns
- **Visual Rule Builder**: GUI for creating custom rules

---

**🎉 Enjoy your intelligently organized windows!** The ordering system transforms chaotic window arrangements into logical, productive layouts that adapt to your workflow.
