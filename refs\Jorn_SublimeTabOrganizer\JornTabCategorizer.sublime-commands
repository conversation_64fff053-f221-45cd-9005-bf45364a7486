[{"caption": "JornTabCategor: Show Tab Overview", "command": "jorn_tab_categorizer", "args": {"display_results": true, "output_format": "table"}}, {"caption": "JornTabCategor: Show Tab Overview (Sorted by Group)", "command": "jorn_tab_categorizer", "args": {"display_results": true, "output_format": "table", "sort_by": "group"}}, {"caption": "JornTabCategor: Show Tab Overview (Sorted by Extension)", "command": "jorn_tab_categorizer", "args": {"display_results": true, "output_format": "table", "sort_by": "extension"}}, {"caption": "JornTabCategor: Export Tabs to Markdown", "command": "jorn_tab_categorizer", "args": {"export_to_file": true, "columns": ["Save Count", "De<PERSON><PERSON>", "Last Saved", "Ext", "File", "Status", "Group", "Index", "Modified", "Lines", "Size", "Last Access", "Category", "Path"]}}, {"caption": "JornTabCategor: Export Tabs to <PERSON><PERSON> (Simplified)", "command": "jorn_tab_categorizer", "args": {"export_to_file": true, "columns": ["Group", "File", "Last Saved", "Save Count", "Ext", "Path"]}}, {"caption": "JornTabCategor: Import Tabs from Markdown", "command": "jorn_tab_categorizer", "args": {"import_from_file": true}}, {"caption": "JornTabCategor: Show Tab Overview (JSON Format)", "command": "jorn_tab_categorizer", "args": {"display_results": true, "output_format": "json"}}, {"caption": "JornTabCategor: Debug Mode - Export Tabs to Markdown", "command": "jorn_tab_categorizer", "args": {"export_to_file": true, "columns": ["Save Count", "De<PERSON><PERSON>", "Last Saved", "Ext", "File", "Status", "Group", "Index", "Modified", "Lines", "Size", "Last Access", "Category", "Path"], "debug": true}}, {"caption": "JornTabCategor: Debug Mode - Import Tabs from Markdown", "command": "jorn_tab_categorizer", "args": {"import_from_file": true, "debug": true}}]